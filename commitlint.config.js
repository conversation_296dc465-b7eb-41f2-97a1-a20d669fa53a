// commitlint.config.js
module.exports = {
  extends: ['@commitlint/config-conventional'], // 检测规则
  rules: {
    // 设置提交人姓名
    'type-enum': [2, 'always', ['qwc', 'fjk', 'mww', 'zsh', 'dyn', 'cqy', 'lsh']],
    'type-case': [0],
    'type-empty': [0],
    'scope-empty': [0],
    'scope-case': [0],
    'subject-full-stop': [0, 'never'],
    'subject-case': [0, 'never'],
    'header-max-length': [0, 'always', 72]
  }
}
