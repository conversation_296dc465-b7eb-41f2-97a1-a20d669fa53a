<script setup lang="ts">
import * as Cesium from 'cesium'

const baseMap = new Cesium.UrlTemplateImageryProvider({
  url: 'https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
  minimumLevel: 2, // 最小缩放级别
  maximumLevel: 15 // 最大缩放级别
})
const defaultToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.Trh3Rr_1gX3oh-ERhIbFrUGvVYwDJCMP1b16r6uv80E'
Cesium.Ion.defaultAccessToken = defaultToken
let protectAreaGeoJson = null
let viewer: Cesium.Viewer
onMounted(async () => {
  await initViewer()
  await loadGeoJson()
})

async function initViewer() {
  viewer = new Cesium.Viewer('cesiumContainer', {
    // 主页按钮
    homeButton: false,
    // 场景模式选择器（2D/3D/Columbus View）
    sceneModePicker: false,
    // 底图选择器
    baseLayerPicker: true,
    // 导航帮助按钮
    navigationHelpButton: false,
    // 动画控件
    animation: false,
    // 时间轴
    timeline: false,
    // 全屏按钮
    fullscreenButton: false,
    // VR 按钮
    vrButton: false,
    // 信息框
    infoBox: true,
    // 选择指示器
    selectionIndicator: true,
    // 地理编码器（搜索框）
    geocoder: false,
    terrainProvider: await Cesium.createWorldTerrainAsync()
  })
  console.log(viewer)
  // 加载底图服务
  viewer.imageryLayers.addImageryProvider(baseMap)
  // 隐藏 logo
  const creditContainer = viewer.cesiumWidget.creditContainer as HTMLElement
  creditContainer.style.display = 'none'
  // try {
  //   const tileset = await Cesium.createOsmBuildingsAsync()
  //   viewer.scene.primitives.add(tileset)
  // } catch (error) {
  //   console.log(`Error creating tileset: ${error}`)
  // }
  // 切换为平面视图（2D 模式）
  // viewer.scene.mode = Cesium.SceneMode.SCENE2D
}

async function loadGeoJson() {
  const geojson = await fetch(`${import.meta.env.VITE_APP_URL}geojson/protect_area.geojson`)
  protectAreaGeoJson = await geojson.json()
  console.log(protectAreaGeoJson)
  let dataSource = await Cesium.GeoJsonDataSource.load(
    `${import.meta.env.VITE_APP_URL}geojson/protect_area.geojson`
  )
  // 处理样式
  let entities = dataSource.entities.values
  entities.forEach((entity) => {
    if (entity.polygon) {
      const bhdlx = entity.properties?.BHDLX.getValue()
      let color = Cesium.Color.GREEN.withAlpha(0.5)
      if (bhdlx === '缓冲区') {
        color = Cesium.Color.GREEN.withAlpha(0.5)
      } else if (bhdlx === '核心区') {
        color = Cesium.Color.YELLOW.withAlpha(0.5)
      } else if (bhdlx === '抢救园') {
        color = Cesium.Color.RED.withAlpha(0.5)
      } else if (bhdlx === '试验区') {
        color = Cesium.Color.BLUE.withAlpha(0.5)
      }
      entity.polygon!.material = new Cesium.ColorMaterialProperty(color)
    }
  })

  viewer.dataSources.add(dataSource)
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(114.312619192922554, 29.987891789199448, 10000)
  })
  // viewer.zoomTo(dataSource) // 调整视图以显示数据
  // console.log('加载数据', dataSource.entities.values)
  // 加载geojson数据
  // const { Color, GeoJsonDataSource, ColorMaterialProperty, ConstantProperty } = Cesium
  // const dataGeo = GeoJsonDataSource.load(
  //   'https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json',
  //   {
  //     stroke: Color.RED,
  //     fill: Color.SKYBLUE.withAlpha(0.5),
  //     strokeWidth: 4
  //   }
  // )
  // dataGeo.then((dataSources) => {
  //   console.log('🚀 ~ dataGeo.then ~ dataSources:', dataSources)
  //   viewer.dataSources.add(dataSources)
  //   const entities = dataSources.entities.values
  //   entities.forEach((entity, i) => {
  //     entity.polygon!.material = new ColorMaterialProperty(
  //       Color.fromRandom({
  //         alpha: 1
  //       })
  //     )
  //     entity.polygon!.outline = new ConstantProperty(false) // 不显示多边形的边界
  //     const randomNum = Math.floor(Math.random() * 5)
  //     entity.polygon!.extrudedHeight = new ConstantProperty(100000 * randomNum) // 拓展高度
  //   })
  // })
}
</script>

<template>
  <div id="cesiumContainer" class="cesium-container"></div>
</template>

<style scoped>
#cesiumContainer {
  width: 100vw;
  height: 100vh;
}
</style>
