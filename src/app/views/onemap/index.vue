<template>
  <div id="cesiumContainer"></div>
</template>

<script lang="ts" setup>
import * as Cesium from 'cesium'
import { useMap } from './hooks/useMap'

let viewer
onMounted(async () => {
  await initViewer()
  const { initBaseMap } = useMap(viewer)
  initBaseMap()
})

async function initViewer() {
  viewer = new Cesium.Viewer('cesiumContainer', {
    homeButton: false,
    sceneModePicker: false,
    baseLayerPicker: true,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: false,
    vrButton: false,
    infoBox: true,
    selectionIndicator: true,
    geocoder: false
  })
  window.viewer = viewer
}
</script>

<style lang="scss" scoped>
#cesiumContainer {
  width: 100vw;
  height: 100vh;
}
</style>
