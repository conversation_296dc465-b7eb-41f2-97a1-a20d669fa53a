<template>
  <div class="map-fly-example">
    <div class="controls">
      <button @click="flyToBeijing">飞行到北京</button>
      <button @click="flyToShanghai">飞行到上海</button>
      <button @click="fitChinaBounds">适应中国边界</button>
      <button @click="fitCustomBounds">适应自定义区域</button>
      <button @click="getCurrentPosition">获取当前位置</button>
    </div>
    <div id="cesiumContainer" class="cesium-container"></div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from 'cesium'
import { useMapFly } from '../hooks/useMapFly'

let viewer: Cesium.Viewer
let mapFly: ReturnType<typeof useMapFly>

onMounted(() => {
  initViewer()
})

const initViewer = () => {
  viewer = new Cesium.Viewer('cesiumContainer', {
    homeButton: false,
    sceneModePicker: false,
    baseLayerPicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: false,
    vrButton: false,
    infoBox: false,
    selectionIndicator: false,
    geocoder: false
  })

  // 初始化 mapFly hook
  mapFly = useMapFly(viewer, {
    duration: 2 // 默认飞行时间2秒
  })
}

// 飞行到北京
const flyToBeijing = async () => {
  try {
    await mapFly.flyTo([116.4074, 39.9042], {
      height: 15000,
      heading: 0,
      pitch: -45,
      duration: 3
    })
    console.log('成功飞行到北京！')
  } catch (error) {
    console.error('飞行到北京失败:', error)
  }
}

// 飞行到上海
const flyToShanghai = async () => {
  try {
    await mapFly.flyTo([121.4737, 31.2304], {
      height: 12000,
      heading: 30,
      pitch: -60,
      duration: 4
    })
    console.log('成功飞行到上海！')
  } catch (error) {
    console.error('飞行到上海失败:', error)
  }
}

// 适应中国边界
const fitChinaBounds = async () => {
  try {
    await mapFly.fitBounds([73.5, 18.2, 134.8, 53.5], {
      padding: 2,
      duration: 5
    })
    console.log('成功适应中国边界！')
  } catch (error) {
    console.error('适应中国边界失败:', error)
  }
}

// 适应自定义区域（长三角地区）
const fitCustomBounds = async () => {
  try {
    await mapFly.fitBounds([118.0, 30.0, 122.0, 33.0], {
      padding: 0.5,
      duration: 3
    })
    console.log('成功适应长三角区域！')
  } catch (error) {
    console.error('适应长三角区域失败:', error)
  }
}

// 获取当前相机位置
const getCurrentPosition = () => {
  const currentView = mapFly.getCurrentView()
  if (currentView) {
    console.log('当前相机位置:', {
      经度: currentView.longitude.toFixed(6),
      纬度: currentView.latitude.toFixed(6),
      高度: currentView.height.toFixed(2),
      方位角: currentView.heading.toFixed(2),
      俯仰角: currentView.pitch.toFixed(2),
      翻滚角: currentView.roll.toFixed(2)
    })
  } else {
    console.log('相机不可用')
  }
}
</script>

<style scoped>
.map-fly-example {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.controls {
  padding: 10px;
  background: #f0f0f0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.controls button {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.controls button:hover {
  background: #40a9ff;
}

.cesium-container {
  flex: 1;
  width: 100%;
}
</style>
