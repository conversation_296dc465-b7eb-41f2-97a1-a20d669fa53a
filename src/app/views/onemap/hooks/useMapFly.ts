import {
  Cartesian3,
  <PERSON><PERSON>ang<PERSON>,
  <PERSON>,
  Matrix4,
  EasingFunction,
  Math as CesiumMath,
  Viewer
} from 'cesium'

// 基础飞行选项接口
type BaseFlightOptions = {
  orientation?: {
    heading?: number
    pitch?: number
    roll?: number
  }
  duration?: number
  complete?: Camera.FlightCompleteCallback
  cancel?: Camera.FlightCancelledCallback
  endTransform?: Matrix4
  maximumHeight?: number
  pitchAdjustHeight?: number
  flyOverLongitude?: number
  flyOverLongitudeWeight?: number
  convert?: boolean
  easingFunction?: EasingFunction.Callback
}

// 原始选项类型，保持向后兼容
type Options = BaseFlightOptions & {
  destination: Cartesian3 | Rectangle
}

// flyTo 方法的配置选项
interface FlyToOptions {
  duration?: number
  height?: number
  heading?: number
  pitch?: number
  roll?: number
  maximumHeight?: number
  pitchAdjustHeight?: number
  flyOverLongitude?: number
  flyOverLongitudeWeight?: number
  convert?: boolean
  easingFunction?: EasingFunction.Callback
  complete?: Camera.FlightCompleteCallback
  cancel?: Camera.FlightCancelledCallback
  endTransform?: Matrix4
}

// fitBounds 方法的配置选项
interface FitBoundsOptions {
  duration?: number
  padding?: number
  maxZoom?: number
  maximumHeight?: number
  pitchAdjustHeight?: number
  flyOverLongitude?: number
  flyOverLongitudeWeight?: number
  convert?: boolean
  easingFunction?: EasingFunction.Callback
  complete?: Camera.FlightCompleteCallback
  cancel?: Camera.FlightCancelledCallback
  endTransform?: Matrix4
}

export function useMapFly(viewer: Viewer, defaultOptions?: Partial<BaseFlightOptions>) {
  const initOptions = ref<Options | null>(null)

  const init = (options: Options) => {
    initOptions.value = options
    if (viewer?.camera) {
      viewer.camera.flyTo(options)
    }
  }

  /**
   * 飞行到指定的经纬度坐标
   * @param coordinates - 长度为2的数组 [longitude, latitude]，表示目标经纬度坐标
   * @param options - 可选的飞行配置参数
   * @returns Promise，在飞行完成时解析
   */
  const flyTo = (coordinates: [number, number], options: FlyToOptions = {}): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!viewer?.camera) {
        reject(new Error('Viewer 或 camera 不可用'))
        return
      }

      if (!Array.isArray(coordinates) || coordinates.length !== 2) {
        reject(new Error('coordinates 必须是长度为2的数组 [longitude, latitude]'))
        return
      }

      const [longitude, latitude] = coordinates
      const {
        height = 10000,
        heading = 0,
        pitch = -90,
        roll = 0,
        duration = 3,
        complete,
        cancel,
        ...restOptions
      } = options

      // 将度数转换为弧度供 Cesium 使用
      const destination = Cartesian3.fromDegrees(longitude, latitude, height)

      const orientation = {
        heading: CesiumMath.toRadians(heading),
        pitch: CesiumMath.toRadians(pitch),
        roll: CesiumMath.toRadians(roll)
      }

      const flightOptions: Options = {
        destination,
        orientation,
        duration,
        complete: () => {
          complete?.()
          resolve()
        },
        cancel: () => {
          cancel?.()
          reject(new Error('飞行被取消'))
        },
        ...defaultOptions,
        ...restOptions
      }

      viewer.camera.flyTo(flightOptions)
    })
  }

  /**
   * 调整相机视角以适应指定的地理边界框
   * @param bounds - 长度为4的数组 [west, south, east, north]，表示地理边界框的西、南、东、北四至坐标
   * @param options - 可选的视图调整配置参数
   * @returns Promise，在视图调整完成时解析
   */
  const fitBounds = (
    bounds: [number, number, number, number],
    options: FitBoundsOptions = {}
  ): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!viewer?.camera) {
        reject(new Error('Viewer 或 camera 不可用'))
        return
      }

      if (!Array.isArray(bounds) || bounds.length !== 4) {
        reject(new Error('bounds 必须是长度为4的数组 [west, south, east, north]'))
        return
      }

      const [west, south, east, north] = bounds
      const { padding = 0, duration = 3, complete, cancel, ...restOptions } = options

      // 创建矩形边界
      const rectangle = Rectangle.fromDegrees(west, south, east, north)

      // 如果指定了填充，则扩展边界
      let destination = rectangle
      if (padding > 0) {
        destination = Rectangle.fromDegrees(
          west - padding,
          south - padding,
          east + padding,
          north + padding
        )
      }

      const flightOptions: Options = {
        destination,
        duration,
        complete: () => {
          complete?.()
          resolve()
        },
        cancel: () => {
          cancel?.()
          reject(new Error('视图调整被取消'))
        },
        ...defaultOptions,
        ...restOptions
      }

      viewer.camera.flyTo(flightOptions)
    })
  }

  /**
   * 获取当前相机的位置和方向信息
   * @returns 当前相机状态，如果相机不可用则返回 null
   */
  const getCurrentView = () => {
    if (!viewer?.camera) {
      return null
    }

    const position = viewer.camera.position
    const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(position)

    return {
      longitude: CesiumMath.toDegrees(cartographic.longitude),
      latitude: CesiumMath.toDegrees(cartographic.latitude),
      height: cartographic.height,
      heading: CesiumMath.toDegrees(viewer.camera.heading),
      pitch: CesiumMath.toDegrees(viewer.camera.pitch),
      roll: CesiumMath.toDegrees(viewer.camera.roll)
    }
  }

  return {
    init,
    flyTo,
    fitBounds,
    getCurrentView,
    initOptions: readonly(initOptions)
  }
}
