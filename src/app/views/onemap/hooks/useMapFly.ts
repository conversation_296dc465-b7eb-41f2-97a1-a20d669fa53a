import {
  Cartesian3,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Matrix4,
  EasingFunction,
  Math as CesiumM<PERSON>,
  Viewer
} from 'cesium'

// Base flight options interface
type BaseFlightOptions = {
  orientation?: {
    heading?: number
    pitch?: number
    roll?: number
  }
  duration?: number
  complete?: Camera.FlightCompleteCallback
  cancel?: Camera.FlightCancelledCallback
  endTransform?: Matrix4
  maximumHeight?: number
  pitchAdjustHeight?: number
  flyOverLongitude?: number
  flyOverLongitudeWeight?: number
  convert?: boolean
  easingFunction?: EasingFunction.Callback
}

// Original options type for backward compatibility
type Options = BaseFlightOptions & {
  destination: Cartesian3 | Rectangle
}

// FlyTo method options
interface FlyToOptions extends BaseFlightOptions {
  longitude: number
  latitude: number
  height?: number
  heading?: number
  pitch?: number
  roll?: number
}

// FitBounds method options
interface FitBoundsOptions extends Omit<BaseFlightOptions, 'orientation'> {
  north: number
  south: number
  east: number
  west: number
  padding?: number
  maxZoom?: number
}

export function useMapFly(viewer: Viewer, defaultOptions?: Partial<BaseFlightOptions>) {
  const initOptions = ref<Options | null>(null)

  const init = (options: Options) => {
    initOptions.value = options
    if (viewer?.camera) {
      viewer.camera.flyTo(options)
    }
  }

  /**
   * Fly to a specific longitude and latitude coordinate
   * @param options - Flight options including required longitude and latitude
   * @returns Promise that resolves when flight is complete
   */
  const flyTo = (options: FlyToOptions): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!viewer?.camera) {
        reject(new Error('Viewer or camera not available'))
        return
      }

      const {
        longitude,
        latitude,
        height = 10000,
        heading = 0,
        pitch = -90,
        roll = 0,
        duration = 3,
        complete,
        cancel,
        ...restOptions
      } = options

      // Convert degrees to radians for Cesium
      const destination = Cartesian3.fromDegrees(longitude, latitude, height)

      const orientation = {
        heading: CesiumMath.toRadians(heading),
        pitch: CesiumMath.toRadians(pitch),
        roll: CesiumMath.toRadians(roll)
      }

      const flightOptions: Options = {
        destination,
        orientation,
        duration,
        complete: () => {
          complete?.()
          resolve()
        },
        cancel: () => {
          cancel?.()
          reject(new Error('Flight was cancelled'))
        },
        ...defaultOptions,
        ...restOptions
      }

      viewer.camera.flyTo(flightOptions)
    })
  }

  /**
   * Fit the camera view to show the specified bounding box
   * @param options - Bounding box options with north, south, east, west coordinates
   * @returns Promise that resolves when flight is complete
   */
  const fitBounds = (options: FitBoundsOptions): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!viewer?.camera) {
        reject(new Error('Viewer or camera not available'))
        return
      }

      const {
        north,
        south,
        east,
        west,
        padding = 0,
        duration = 3,
        complete,
        cancel,
        ...restOptions
      } = options

      // Create a Rectangle from the bounds
      const rectangle = Rectangle.fromDegrees(west, south, east, north)

      // Apply padding if specified
      let destination = rectangle
      if (padding > 0) {
        destination = Rectangle.fromDegrees(
          west - padding,
          south - padding,
          east + padding,
          north + padding
        )
      }

      const flightOptions: Options = {
        destination,
        duration,
        complete: () => {
          complete?.()
          resolve()
        },
        cancel: () => {
          cancel?.()
          reject(new Error('Flight was cancelled'))
        },
        ...defaultOptions,
        ...restOptions
      }

      viewer.camera.flyTo(flightOptions)
    })
  }

  /**
   * Get current camera position and orientation
   * @returns Current camera state
   */
  const getCurrentView = () => {
    if (!viewer?.camera) {
      return null
    }

    const position = viewer.camera.position
    const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(position)

    return {
      longitude: CesiumMath.toDegrees(cartographic.longitude),
      latitude: CesiumMath.toDegrees(cartographic.latitude),
      height: cartographic.height,
      heading: CesiumMath.toDegrees(viewer.camera.heading),
      pitch: CesiumMath.toDegrees(viewer.camera.pitch),
      roll: CesiumMath.toDegrees(viewer.camera.roll)
    }
  }

  return {
    init,
    flyTo,
    fitBounds,
    getCurrentView,
    initOptions: readonly(initOptions)
  }
}
