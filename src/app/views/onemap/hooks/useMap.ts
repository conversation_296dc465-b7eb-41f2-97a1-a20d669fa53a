import {
  Viewer,
  UrlTemplateImageryProvider,
  GeoJsonDataSource,
  ColorMaterialProperty,
  Color,
  ConstantProperty,
  SceneMode,
  Cartesian3,
  Math,
  Cartographic,
  ShadowMode,
  ScreenSpaceEventHandler,
  defined,
  ScreenSpaceEventType,
  Entity
} from 'cesium'
import _ from 'lodash'
export const useMap = (Viewer: Viewer) => {
  const city_geojson = ref<any>(null)

  const initBaseMap = async () => {
    const destination = Cartesian3.fromDegrees(112.60825577625415, 31.068980072116183, 600000)
    // 初始化四至视角
    Viewer.camera.setView({
      destination,
      orientation: {
        heading: Math.toRadians(0),
        pitch: Math.toRadians(-45),
        roll: 0
      }
    })
    const longitude = Math.toDegrees(Cartographic.fromCartesian(destination).longitude)
    const latitude = Math.toDegrees(Cartographic.fromCartesian(destination).latitude)
    console.log(longitude, latitude) // 输出类似 12, 2
    // 设置底图
    // Viewer.imageryLayers.addImageryProvider(
    //   new UrlTemplateImageryProvider({
    //     url: 'https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
    //     minimumLevel: 2, // 最小缩放级别
    //     maximumLevel: 15 // 最大缩放级别
    //   })
    // )
    Viewer.scene.globe.enableLighting = false
    Viewer.scene.mode = SceneMode.COLUMBUS_VIEW
    // 设置限制
    astrictViewer()
    await loadGeojson()
    console.log(city_geojson.value)
    initBaseLayers()
    listenerEvent()
  }
  const astrictViewer = () => {
    // 设置相机控制器限制
    const controller = Viewer.scene.screenSpaceCameraController

    // 1. 限制缩放级别（高度范围）
    controller.minimumZoomDistance = 100 // 最小高度100米
    controller.maximumZoomDistance = 5000000 // 最大高度5000万米

    // 2. 启用碰撞检测，防止进入地下
    controller.enableCollisionDetection = true
  }

  const loadGeojson = async () => {
    city_geojson.value = await GeoJsonDataSource.load(
      `${import.meta.env.VITE_APP_URL}geojson/city.geojson`
    )
  }

  const initBaseLayers = async () => {
    Viewer.dataSources.add(city_geojson.value)
    const city_entities = city_geojson.value.entities.values
    city_entities.forEach((entity, i) => {
      entity.polygon!.material = new ColorMaterialProperty(Color.fromCssColorString('#fff'))
      entity.polygon!.outline = new ConstantProperty(false) // 不显示多边形的边界
      entity.polygon!.extrudedHeight = new ConstantProperty(10000) // 拓展高度
      entity.polygon!.shadows = ShadowMode.DISABLED
      entity.polygon!.outline = true
      entity.polygon!.outlineColor = Color.fromCssColorString('#fff')
    })
  }
  const locationInfo = ref()
  const listenerEvent = () => {
    // // 创建 ScreenSpaceEventHandler 实例
    // const handler = new ScreenSpaceEventHandler(Viewer.scene.canvas)

    // // 监听鼠标移动事件
    // handler.setInputAction(function (movement) {
    //   // 获取鼠标指向的 Entity
    //   const pickedObject = Viewer.scene.pick(movement.endPosition)

    //   // 判断是否指向了 Entity
    //   if (defined(pickedObject) && pickedObject.id instanceof Entity) {
    //     const entity = pickedObject.id
    //     console.log('entity.polygon', entity)

    //     // 高亮 Entity（例如改变颜色）
    //     entity.polygon.material = new ColorMaterialProperty(Color.RED)
    //     entity.polygon!.extrudedHeight = new ConstantProperty(20000) // 拓展高度
    //   } else {
    //     console.log('Viewer.entities.values', Viewer.entities.values)
    //     const city_entities = city_geojson.value.entities.values
    //     // 恢复所有 Entity 的默认样式
    //     city_entities.forEach((entity) => {
    //       if (entity.polygon) {
    //         entity.polygon!.material = new ColorMaterialProperty(Color.WHITE)
    //         entity.polygon!.extrudedHeight = new ConstantProperty(10000) // 拓展高度
    //       }
    //     })
    //   }
    // }, ScreenSpaceEventType.MOUSE_MOVE)
    // 监听鼠标悬停
    viewer.scene.postRender.addEventListener(() => {
      const pickedObject = viewer.scene.pick(viewer.scene.camera.position)
      if (pickedObject && pickedObject.id === entity) {
        entity.polyline.material = Cesium.Color.YELLOW
      } else {
        entity.polyline.material = Cesium.Color.RED
      }
    })
  }

  return {
    initBaseMap
  }
}
