import {
  Viewer,
  UrlTemplateImageryProvider,
  GeoJsonDataSource,
  ColorMaterialProperty,
  Color,
  ConstantProperty,
  SceneMode,
  Cartesian3,
  Math,
  Cartographic
} from 'cesium'
export const useMap = (Viewer: Viewer) => {
  const city_geojson = ref<any>(null)

  const initBaseMap = async () => {
    // 初始化四至视角
    Viewer.camera.setView({
      destination: Cartesian3.fromDegrees(113.48328032298093, 31.00720445678138, 300000),
      orientation: {
        heading: Math.toRadians(0),
        pitch: Math.toRadians(-45),
        roll: 0
      }
    })
    // 设置底图
    // Viewer.imageryLayers.addImageryProvider(
    //   new UrlTemplateImageryProvider({
    //     url: 'https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
    //     minimumLevel: 2, // 最小缩放级别
    //     maximumLevel: 15 // 最大缩放级别
    //   })
    // )
    Viewer.scene.globe.enableLighting = false
    Viewer.scene.mode = SceneMode.COLUMBUS_VIEW
    await loadGeojson()
    console.log(city_geojson.value)
    initBaseLayers()
    listenerEvent()
  }

  const loadGeojson = async () => {
    city_geojson.value = await GeoJsonDataSource.load(
      `${import.meta.env.VITE_APP_URL}geojson/city.geojson`
    )
  }

  const initBaseLayers = async () => {
    Viewer.dataSources.add(city_geojson.value)
    const city_entities = city_geojson.value.entities.values
    city_entities.forEach((entity, i) => {
      entity.polygon!.material = new ColorMaterialProperty(Color.fromCssColorString('#fff'))
      entity.polygon!.outline = new ConstantProperty(false) // 不显示多边形的边界
      entity.polygon!.extrudedHeight = new ConstantProperty(10000) // 拓展高度
    })
  }

  const listenerEvent = () => {
    Viewer.camera.changed.addEventListener(() => {
      const position = Viewer.camera.position

      console.log(position)

      const cartographic = Cartographic.fromCartesian(position)

      const longitude = cartographic.longitude
      const latitude = cartographic.latitude
      const height = cartographic.height

      console.log(`相机位置 - 经度: ${longitude}, 纬度:${latitude}, 高度: ${height}`)
    })
  }

  return {
    initBaseMap
  }
}
