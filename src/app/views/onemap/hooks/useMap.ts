import {
  Viewer,
  GeoJsonDataSource,
  ColorMaterialProperty,
  Color,
  ConstantProperty,
  SceneMode,
  Cartesian3,
  Math,
  Cartographic,
  ShadowMode,
  ScreenSpaceEventHandler,
  defined,
  ScreenSpaceEventType,
  Entity
} from 'cesium'
import _ from 'lodash'
export const useMap = (Viewer: Viewer) => {
  const city_geojson = ref<any>(null)

  // 用于跟踪当前高亮的实体和其原始属性
  let currentHighlightedEntity: Entity | null = null
  const originalProperties = new Map<Entity, { height: number; color: Color }>()

  // 防抖处理，避免频繁更新
  const debouncedMouseMove = _.debounce((pickedEntity: Entity | null) => {
    handleEntityHover(pickedEntity)
  }, 16) // 约60fps的更新频率

  const initBaseMap = async () => {
    const destination = Cartesian3.fromDegrees(112.60825577625415, 31.068980072116183, 600000)
    // 初始化四至视角
    Viewer.camera.setView({
      destination,
      orientation: {
        heading: Math.toRadians(0),
        pitch: Math.toRadians(-45),
        roll: 0
      }
    })
    const longitude = Math.toDegrees(Cartographic.fromCartesian(destination).longitude)
    const latitude = Math.toDegrees(Cartographic.fromCartesian(destination).latitude)
    console.log(longitude, latitude) // 输出类似 12, 2
    // 设置底图
    // Viewer.imageryLayers.addImageryProvider(
    //   new UrlTemplateImageryProvider({
    //     url: 'https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
    //     minimumLevel: 2, // 最小缩放级别
    //     maximumLevel: 15 // 最大缩放级别
    //   })
    // )
    Viewer.scene.globe.enableLighting = false
    Viewer.scene.mode = SceneMode.COLUMBUS_VIEW
    // 设置限制
    astrictViewer()
    await loadGeojson()
    console.log(city_geojson.value)
    initBaseLayers()
    listenerEvent()
  }
  const astrictViewer = () => {
    // 设置相机控制器限制
    const controller = Viewer.scene.screenSpaceCameraController

    // 1. 限制缩放级别（高度范围）
    controller.minimumZoomDistance = 100 // 最小高度100米
    controller.maximumZoomDistance = 5000000 // 最大高度5000万米

    // 2. 启用碰撞检测，防止进入地下
    controller.enableCollisionDetection = true
  }

  const loadGeojson = async () => {
    city_geojson.value = await GeoJsonDataSource.load(
      `${import.meta.env.VITE_APP_URL}geojson/city.geojson`
    )
  }

  const initBaseLayers = async () => {
    Viewer.dataSources.add(city_geojson.value)
    const city_entities = city_geojson.value.entities.values
    city_entities.forEach((entity: any) => {
      const originalColor = Color.fromCssColorString('#fff')
      const originalHeight = 10000

      entity.polygon!.material = new ColorMaterialProperty(originalColor)
      entity.polygon!.outline = new ConstantProperty(false) // 不显示多边形的边界
      entity.polygon!.extrudedHeight = new ConstantProperty(originalHeight) // 拓展高度
      entity.polygon!.shadows = ShadowMode.DISABLED
      entity.polygon!.outline = true
      entity.polygon!.outlineColor = Color.fromCssColorString('#fff')

      // 保存原始属性
      originalProperties.set(entity, {
        height: originalHeight,
        color: originalColor
      })
    })
  }

  /**
   * 处理实体悬停效果
   * @param pickedEntity - 鼠标下的实体，null表示没有实体
   */
  const handleEntityHover = (pickedEntity: Entity | null) => {
    // 如果当前有高亮实体且不是新选中的实体，则恢复原始状态
    if (currentHighlightedEntity && currentHighlightedEntity !== pickedEntity) {
      restoreEntityOriginalState(currentHighlightedEntity)
    }

    // 如果有新的实体被选中
    if (pickedEntity && isCityEntity(pickedEntity)) {
      highlightEntity(pickedEntity)
      currentHighlightedEntity = pickedEntity
    } else {
      currentHighlightedEntity = null
    }
  }

  /**
   * 检查实体是否属于城市实体
   * @param entity - 要检查的实体
   * @returns 是否为城市实体
   */
  const isCityEntity = (entity: Entity): boolean => {
    if (!city_geojson.value) return false
    const cityEntities = city_geojson.value.entities.values
    return cityEntities.includes(entity)
  }

  /**
   * 高亮显示实体
   * @param entity - 要高亮的实体
   */
  const highlightEntity = (entity: Entity) => {
    if (!entity.polygon) return

    // 设置高亮效果
    entity.polygon.material = new ColorMaterialProperty(Color.BLUE) // 设置为蓝色
    entity.polygon.extrudedHeight = new ConstantProperty(20000) // 增加高度
  }

  /**
   * 恢复实体的原始状态
   * @param entity - 要恢复的实体
   */
  const restoreEntityOriginalState = (entity: Entity) => {
    if (!entity.polygon) return

    const originalProps = originalProperties.get(entity)
    if (originalProps) {
      entity.polygon.extrudedHeight = new ConstantProperty(originalProps.height)
      entity.polygon.material = new ColorMaterialProperty(originalProps.color)
    }
  }
  const listenerEvent = () => {
    // 创建屏幕空间事件处理器
    const handler = new ScreenSpaceEventHandler(Viewer.scene.canvas)

    // 监听鼠标移动事件
    handler.setInputAction((event: any) => {
      // 获取鼠标位置下的实体
      const pickedObject = Viewer.scene.pick(event.endPosition)
      let pickedEntity: Entity | null = null

      if (defined(pickedObject) && defined(pickedObject.id)) {
        pickedEntity = pickedObject.id as Entity
      }

      // 使用防抖处理，避免频繁更新
      debouncedMouseMove(pickedEntity)
    }, ScreenSpaceEventType.MOUSE_MOVE)

    // 监听相机位置变化（保持原有功能）
    Viewer.camera.changed.addEventListener(() => {
      const position = Viewer.camera.position
      const cartographic = Cartographic.fromCartesian(position)

      const longitude = Math.toDegrees(cartographic.longitude)
      const latitude = Math.toDegrees(cartographic.latitude)
      const height = cartographic.height

      console.log(`相机位置 - 经度: ${longitude}, 纬度: ${latitude}, 高度: ${height}`)
    })

    // 监听鼠标离开画布事件，确保清除高亮状态
    const canvas = Viewer.scene.canvas
    canvas.addEventListener('mouseleave', () => {
      if (currentHighlightedEntity) {
        restoreEntityOriginalState(currentHighlightedEntity)
        currentHighlightedEntity = null
      }
    })

    // 返回处理器以便后续清理
    return handler
  }

  /**
   * 清理事件监听器和资源
   */
  const cleanup = () => {
    // 恢复所有高亮状态
    if (currentHighlightedEntity) {
      restoreEntityOriginalState(currentHighlightedEntity)
      currentHighlightedEntity = null
    }

    // 清理原始属性映射
    originalProperties.clear()
  }

  return {
    initBaseMap,
    cleanup
  }
}
